@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
	:root {
		--radius: 0.5rem;
	}
}

@layer utilities {
	.animate-in {
		animation: animate-in 0.5s ease-out;
	}

	.fade-in {
		animation: fade-in 0.6s ease-out;
	}
}

@keyframes animate-in {
	from {
		opacity: 0;
		transform: translateY(20px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

@keyframes fade-in {
	from {
		opacity: 0;
	}
	to {
		opacity: 1;
	}
}

body {
	font-family: "Montserrat", sans-serif;
}

::-webkit-scrollbar {
	width: 0px;
	height: 0px;
}

::-webkit-scrollbar-track {
	background-color: #f5f5f5;
	border-radius: 10px;
}

::-webkit-scrollbar-thumb {
	background-color: #00bcd4;
	border-radius: 10px;
	border: 2px solid #f5f5f5;
}

::-webkit-scrollbar-thumb:hover {
	background-color: #0097a7;
}
