import { MainSkeletonContainer } from "@/shared/components/container/main-skeleton-container";
import { CardLMPContainer } from "@/shared/components/custom/card";
import { Pagination } from "@/shared/components/custom/pagination";
import { usePagination } from "@/shared/hooks/utils/use-pagination.hook";
import { BanknoteIcon, Filter, Home, Receipt } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { IoAdd } from "react-icons/io5";
import { BillsListMobile } from "../components/bills/bills-list-mobile";
import { BillsTable } from "../components/bills/bills-table";
import { CreateBillModal } from "../components/bills/create-bill-modal";
import ErrorState from "../components/bills/error-state";
import { FilterForm } from "../components/bills/filter-form";
import LoadingState from "../components/bills/loading-state";
import NoRecordsState from "../components/bills/no-records-state";
import { SummaryCards } from "../components/bills/summary-cards";
import { FINANCIAL_CONFIG } from "../data/financial-config";
import { useFindAllBills } from "../hooks/bills/find-all.hook";

type AccountFilters = {
	type?: string;
	description?: string;
	dueDate?: Date | null;
	paymentDate?: Date | null;
	personId?: string;
	accountId?: string;
};

export const ContasPage = () => {
	const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
	const methods = useForm<AccountFilters>({
		defaultValues: {
			type: "",
			description: "",
			dueDate: null,
			paymentDate: null,
			personId: "",
			accountId: "",
		},
	});

	const { page, itemsPerPage, handlePageChange, handleItemsPerPageChange } = usePagination(10);

	const { bills, isLoading, error } = useFindAllBills({
		page,
		limit: itemsPerPage,
		type: methods.watch("type") ? Number(methods.watch("type")) : undefined,
		description: methods.watch("description"),
		dueDate: methods.watch("dueDate") ? methods.watch("dueDate")?.toISOString?.() : undefined,
		paymentDate: methods.watch("paymentDate") ? methods.watch("paymentDate")?.toISOString?.() : undefined,
		personId: methods.watch("personId") ? Number(methods.watch("personId")) : undefined,
		accountId: methods.watch("accountId") ? Number(methods.watch("accountId")) : undefined,
	});

	const billsWithStatus = (bills?.data || []).map(bill => ({
		...bill,
	}));

	return (
		<MainSkeletonContainer
			iconTitle={FINANCIAL_CONFIG.Icon}
			itemsBreadcrumb={[
				{ href: "/", label: "Página inicial", icon: Home },
				{
					href: FINANCIAL_CONFIG.path,
					label: "Financeiro",
					icon: BanknoteIcon,
				},
			]}
			currentBreadcrumb={{
				href: FINANCIAL_CONFIG.subItems?.find(item => item.id === "contas")?.path ?? "",
				label: "Contas a Pagar",
				icon: FINANCIAL_CONFIG.subItems?.find(item => item.id === "contas")?.Icon,
			}}
			pageTitle="Contas a Pagar"
		>
			<SummaryCards />

			<CardLMPContainer
				icon={<Filter size={22} className="text-gray-500" />}
				title="Filtros"
				description="Utilize os filtros abaixo para refinar sua busca"
			>
				<FilterForm methods={methods} onNewBill={() => setIsCreateModalOpen(true)} />
			</CardLMPContainer>

			<CardLMPContainer
				title="Lista de Contas"
				description="Gerencie todas as suas contas a pagar"
				icon={<Receipt size={22} className="text-gray-500" />}
			>
				{isLoading ? (
					<LoadingState />
				) : error ? (
					<ErrorState message="Erro ao carregar contas." onRetry={() => methods.handleSubmit(() => {})()} />
				) : !billsWithStatus.length ? (
					<NoRecordsState />
				) : (
					<>
						<div className="hidden md:block">
							<BillsTable accounts={billsWithStatus} />
						</div>
						<BillsListMobile accounts={billsWithStatus} />
						<Pagination
							totalPages={bills?.total ?? 1}
							itemsPerPage={itemsPerPage}
							page={page}
							onPageChange={handlePageChange}
							onItemsPerPageChange={handleItemsPerPageChange}
						/>
					</>
				)}{" "}
			</CardLMPContainer>

			<button
				onClick={() => setIsCreateModalOpen(true)}
				className="md:hidden fixed bottom-24 right-4 z-[60] bg-mainColor text-white rounded-full w-16 h-16 flex items-center justify-center shadow-xl border-4 border-white hover:scale-105 active:scale-95 transition-all duration-200"
				aria-label="Adicionar nova conta"
			>
				<IoAdd size={28} />
			</button>

			<CreateBillModal isOpen={isCreateModalOpen} onClose={() => setIsCreateModalOpen(false)} />
		</MainSkeletonContainer>
	);
};
